#!/bin/bash

# Required parameters:
# @raycast.schemaVersion 1
# @raycast.title Fetch Password
# @raycast.mode fullOutput

# Optional parameters:
# @raycast.icon 🔐
# @raycast.description Fetch http://test.com and find the password

# Documentation:
# @raycast.author raikyou
# @raycast.authorURL https://raycast.com/raikyou

# Fetch the content from test.com
echo "Fetching content from http://test.com..."
RESPONSE=$(curl -s http://test.com)

# Check if curl was successful
if [ $? -ne 0 ]; then
    echo "❌ Error: Failed to fetch content from http://test.com"
    exit 1
fi

# Check if response is empty
if [ -z "$RESPONSE" ]; then
    echo "❌ Error: Empty response from http://test.com"
    exit 1
fi

echo "✅ Successfully fetched content"
echo ""

# Look for password in various common patterns
PASSWORD=""

# Pattern 1: password: value
PASSWORD=$(echo "$RESPONSE" | grep -i "password" | sed -n 's/.*password[[:space:]]*[:=][[:space:]]*\([^[:space:]]*\).*/\1/ip' | head -1)

# Pattern 2: pwd: value
if [ -z "$PASSWORD" ]; then
    PASSWORD=$(echo "$RESPONSE" | grep -i "pwd" | sed -n 's/.*pwd[[:space:]]*[:=][[:space:]]*\([^[:space:]]*\).*/\1/ip' | head -1)
fi

# Pattern 3: pass: value
if [ -z "$PASSWORD" ]; then
    PASSWORD=$(echo "$RESPONSE" | grep -i "pass" | sed -n 's/.*pass[[:space:]]*[:=][[:space:]]*\([^[:space:]]*\).*/\1/ip' | head -1)
fi

# Pattern 4: Look for quoted passwords
if [ -z "$PASSWORD" ]; then
    PASSWORD=$(echo "$RESPONSE" | grep -i "password" | sed -n 's/.*password[[:space:]]*[:=][[:space:]]*["\x27]\([^"\x27]*\)["\x27].*/\1/ip' | head -1)
fi

# Pattern 5: JSON format "password": "value"
if [ -z "$PASSWORD" ]; then
    PASSWORD=$(echo "$RESPONSE" | grep -o '"password"[[:space:]]*:[[:space:]]*"[^"]*"' | sed 's/.*"password"[[:space:]]*:[[:space:]]*"\([^"]*\)".*/\1/' | head -1)
fi

# Display results
if [ -n "$PASSWORD" ]; then
    echo "🔑 Password found: $PASSWORD"
    
    # Copy to clipboard
    echo -n "$PASSWORD" | pbcopy
    echo "📋 Password copied to clipboard"
else
    echo "❌ No password found in the response"
    echo ""
    echo "Response content preview:"
    echo "------------------------"
    echo "$RESPONSE" | head -10
    if [ $(echo "$RESPONSE" | wc -l) -gt 10 ]; then
        echo "... (truncated)"
    fi
fi
